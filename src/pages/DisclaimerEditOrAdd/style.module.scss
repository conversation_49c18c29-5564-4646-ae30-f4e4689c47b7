.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    color: #262626;
  }
}

.closeButton {
  font-size: 18px;
  color: #8c8c8c;

  &:hover {
    color: #262626;
  }
}

.sectionCard {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;

  :global(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 8px 8px 0 0;
  }

  :global(.ant-card-head-title) {
    font-weight: 600;
    color: #262626;
  }
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sectionIcon {
  color: #1890ff;
  font-size: 16px;
}

.sectionDescription {
  margin-bottom: 16px;
  padding: 8px 0;
}

.formField {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.statusToggle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.statusText {
  font-weight: 500;
  color: #262626;
}

.advancedContent {
  margin-top: 16px;
}

.validationErrors {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 6px;

  .errorList {
    margin: 8px 0 0 0;
    padding-left: 20px;

    .errorItem {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.ruleCard {
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

.ruleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h5 {
    margin: 0;
    color: #262626;
  }
}

.deleteButton {
  color: #ff4d4f;

  &:hover {
    background-color: #fff2f0;
  }
}

.ruleContent {
  padding: 0;
}

.categorySelect {
  margin-bottom: 16px;
}

.attributeRule {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;

  &:last-child {
    margin-bottom: 0;
  }
}

.attributeRuleHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.noRules {
  text-align: center;
  padding: 32px;
  color: #8c8c8c;
}

.ruleSummary {
  text-align: center;
  background: #f9fafb;
  color: #000;
  padding: 24px;
  .ruleSummaryTitle {
    font-weight: 500;
  }
  .ant-typography {
    margin-bottom: 8px;
  }
}

.ruleSummaryContent {
  color: #7d8b9e;
  line-height: 1.6;
  margin-top: 10px;
}

.documentUploaded {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  margin-bottom: 12px;
}

.documentInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkIcon {
  color: #52c41a;
  font-size: 16px;
}

.documentName {
  font-weight: 500;
  color: #262626;
}

.uploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
  }
}

.uploadIcon {
  font-size: 32px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.uploadNote {
  margin-top: 8px;
  font-size: 12px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .footer {
    justify-content: center;
  }

  .attributeRule {
    .ant-row {
      .ant-col {
        margin-bottom: 12px;
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .container {
    background-color: #141414;
  }

  .sectionCard {
    background-color: #1f1f1f;

    :global(.ant-card-head) {
      background-color: #262626;
      border-bottom-color: #434343;
    }
  }

  .header,
  .footer {
    background-color: #1f1f1f;
  }

  .ruleCard {
    background-color: #262626;
    border-color: #434343;
  }

  .attributeRule {
    background-color: #262626;
    border-color: #434343;
  }

  .uploadArea {
    background-color: #262626;
    border-color: #434343;

    &:hover {
      background-color: #1f1f1f;
      border-color: #1890ff;
    }
  }

  .documentUploaded {
    background-color: #162312;
    border-color: #49aa19;
  }
}
